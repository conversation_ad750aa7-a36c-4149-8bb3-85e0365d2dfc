<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Frontend API Calls</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, button { padding: 10px; font-size: 16px; }
        input[type="text"], input[type="url"] { width: 100%; box-sizing: border-box; }
        button { background: #007cba; color: white; border: none; cursor: pointer; margin: 5px; }
        button:hover { background: #005a87; }
        button:disabled { background: #ccc; cursor: not-allowed; }
        .log { background: #f5f5f5; padding: 10px; margin-top: 10px; border-radius: 5px; font-family: monospace; font-size: 12px; max-height: 400px; overflow-y: auto; }
        .error { color: red; }
        .success { color: green; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Debug Frontend API Calls</h1>
        
        <div class="form-group">
            <label for="apiBaseUrl">API Base URL:</label>
            <input type="text" id="apiBaseUrl" value="http://localhost:8001" placeholder="http://localhost:8001">
        </div>
        
        <div class="form-group">
            <label for="toolUrl">Tool URL:</label>
            <input type="url" id="toolUrl" value="https://example.com" placeholder="https://example.com">
        </div>
        
        <div class="form-group">
            <label for="toolName">Tool Name:</label>
            <input type="text" id="toolName" value="Test Tool" placeholder="Test Tool">
        </div>
        
        <button onclick="testHealthCheck()">Test Health Check</button>
        <button onclick="testProcessSingleUrl()">Test Process Single URL</button>
        <button onclick="testStartEnhancedScraping()">Test Start Enhanced Scraping</button>
        <button onclick="clearLog()">Clear Log</button>
        
        <div id="log" class="log"></div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : '';
            logDiv.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        function getApiBaseUrl() {
            return document.getElementById('apiBaseUrl').value.trim();
        }

        async function testHealthCheck() {
            const apiBaseUrl = getApiBaseUrl();
            log(`🔍 Testing health check: ${apiBaseUrl}/api/health`);
            
            try {
                const response = await fetch(`${apiBaseUrl}/api/health`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                log(`📊 Response status: ${response.status} ${response.statusText}`);
                log(`📊 Response headers: ${JSON.stringify(Object.fromEntries(response.headers))}`);
                
                const data = await response.json();
                log(`✅ Health check response: ${JSON.stringify(data, null, 2)}`, 'success');
                
            } catch (error) {
                log(`❌ Health check failed: ${error.message}`, 'error');
                log(`❌ Error details: ${error.stack}`, 'error');
            }
        }

        async function testProcessSingleUrl() {
            const apiBaseUrl = getApiBaseUrl();
            const toolUrl = document.getElementById('toolUrl').value.trim();
            const toolName = document.getElementById('toolName').value.trim();
            
            if (!toolUrl) {
                log('❌ Please enter a tool URL', 'error');
                return;
            }
            
            const requestData = {
                name: toolName || 'Test Tool',
                url: toolUrl,
                use_parallel: true,
                use_phase3: true
            };
            
            log(`🚀 Testing process single URL: ${apiBaseUrl}/api/process-single-url`);
            log(`📝 Request data: ${JSON.stringify(requestData, null, 2)}`);
            
            try {
                const response = await fetch(`${apiBaseUrl}/api/process-single-url`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });
                
                log(`📊 Response status: ${response.status} ${response.statusText}`);
                log(`📊 Response headers: ${JSON.stringify(Object.fromEntries(response.headers))}`);
                
                const data = await response.json();
                log(`✅ Process single URL response: ${JSON.stringify(data, null, 2)}`, 'success');
                
                if (data.success && data.job_id) {
                    log(`🔄 Starting to poll job status for: ${data.job_id}`);
                    pollJobStatus(data.job_id);
                }
                
            } catch (error) {
                log(`❌ Process single URL failed: ${error.message}`, 'error');
                log(`❌ Error details: ${error.stack}`, 'error');
            }
        }

        async function testStartEnhancedScraping() {
            const apiBaseUrl = getApiBaseUrl();
            const toolUrl = document.getElementById('toolUrl').value.trim();
            const toolName = document.getElementById('toolName').value.trim();
            
            if (!toolUrl) {
                log('❌ Please enter a tool URL', 'error');
                return;
            }
            
            const requestData = {
                tools: [
                    {
                        name: toolName || 'Test Tool',
                        url: toolUrl
                    }
                ],
                use_parallel: true,
                use_phase3: true
            };
            
            log(`🚀 Testing start enhanced scraping: ${apiBaseUrl}/api/start-enhanced-scraping`);
            log(`📝 Request data: ${JSON.stringify(requestData, null, 2)}`);
            
            try {
                const response = await fetch(`${apiBaseUrl}/api/start-enhanced-scraping`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });
                
                log(`📊 Response status: ${response.status} ${response.statusText}`);
                log(`📊 Response headers: ${JSON.stringify(Object.fromEntries(response.headers))}`);
                
                const data = await response.json();
                log(`✅ Start enhanced scraping response: ${JSON.stringify(data, null, 2)}`, 'success');
                
                if (data.success && data.job_id) {
                    log(`🔄 Starting to poll job status for: ${data.job_id}`);
                    pollJobStatus(data.job_id);
                }
                
            } catch (error) {
                log(`❌ Start enhanced scraping failed: ${error.message}`, 'error');
                log(`❌ Error details: ${error.stack}`, 'error');
            }
        }

        async function pollJobStatus(jobId) {
            const apiBaseUrl = getApiBaseUrl();
            let attempts = 0;
            const maxAttempts = 30;
            
            const poll = async () => {
                attempts++;
                try {
                    const response = await fetch(`${apiBaseUrl}/api/job-status/${jobId}`);
                    const data = await response.json();
                    
                    log(`📊 Job ${jobId} status (${attempts}/${maxAttempts}): ${data.status} - ${data.progress || 0}% - ${data.results?.status || 'N/A'}`);
                    
                    if (data.status === 'completed' || data.status === 'failed' || attempts >= maxAttempts) {
                        log(`🏁 Job ${jobId} finished with status: ${data.status}`, data.status === 'completed' ? 'success' : 'error');
                        if (data.results) {
                            log(`📋 Final results: ${JSON.stringify(data.results, null, 2)}`);
                        }
                        return;
                    }
                    
                    setTimeout(poll, 2000);
                } catch (error) {
                    log(`❌ Error polling job ${jobId}: ${error.message}`, 'error');
                }
            };
            
            poll();
        }

        // Test connectivity on load
        window.onload = async () => {
            log('🌐 Page loaded, testing backend connectivity...');
            await testHealthCheck();
        };
    </script>
</body>
</html>
