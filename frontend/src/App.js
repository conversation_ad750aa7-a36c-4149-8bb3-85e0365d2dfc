import React, { useState, useEffect } from 'react';
import axios from 'axios';
import './App.css';

const API_BASE_URL = process.env.REACT_APP_BACKEND_URL || '';

function App() {
  const [spiders, setSpiders] = useState([]);
  const [pipelineStatus, setPipelineStatus] = useState({});
  const [jobsData, setJobsData] = useState({});
  const [serviceStatus, setServiceStatus] = useState({});
  const [scrapingResults, setScrapingResults] = useState({});
  const [logs, setLogs] = useState([]);
  const [missingTaxonomy, setMissingTaxonomy] = useState([]);
  const [loading, setLoading] = useState(false);
  const [paginationState, setPaginationState] = useState({});
  const [selectedSpider, setSelectedSpider] = useState('');
  const [maxItems, setMaxItems] = useState(50);
  const [startPage, setStartPage] = useState(1);
  const [maxPages, setMaxPages] = useState(0);
  const [activeTab, setActiveTab] = useState('dashboard');

  // Phase 3 Enhanced Features State
  const [capabilities, setCapabilities] = useState({});
  const [enhancedJobs, setEnhancedJobs] = useState({});
  const [performanceDashboard, setPerformanceDashboard] = useState({});
  const [toolsToProcess, setToolsToProcess] = useState([
    { name: 'ChatGPT', url: 'https://chat.openai.com' },
    { name: 'Notion', url: 'https://www.notion.so' }
  ]);
  const [useParallel, setUseParallel] = useState(true);
  const [usePhase3, setUsePhase3] = useState(true);

  // Single URL Processing State
  const [singleUrl, setSingleUrl] = useState('');
  const [singleUrlName, setSingleUrlName] = useState('');
  const [singleUrlJobs, setSingleUrlJobs] = useState({});

  // Fetch initial data
  useEffect(() => {
    fetchSpiders();
    fetchPipelineStatus();
    fetchServiceStatus();
    fetchCapabilities();
    fetchPerformanceDashboard();
    fetchPaginationState();

    // Set up polling for status updates (reduced frequency to prevent server overload)
    const interval = setInterval(() => {
      fetchPipelineStatus();
      fetchPerformanceDashboard();
      updateEnhancedJobStatuses();
    }, 10000); // Changed from 3000ms (3s) to 10000ms (10s)

    return () => clearInterval(interval);
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const fetchSpiders = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/spiders`);
      setSpiders(response.data.spiders);
      if (response.data.spiders.length > 0) {
        setSelectedSpider(response.data.spiders[0]);
      }
    } catch (error) {
      console.error('Failed to fetch spiders:', error);
    }
  };

  const fetchPipelineStatus = async () => {
    try {
      // Fetch current job status for the status display
      const statusResponse = await axios.get(`${API_BASE_URL}/api/status`);
      setPipelineStatus(statusResponse.data);

      // Fetch jobs data for the traditional jobs list
      console.log('🔍 DEBUG: Fetching jobs from /api/jobs');
      const jobsResponse = await axios.get(`${API_BASE_URL}/api/jobs`);
      console.log('🔍 DEBUG: Jobs response:', jobsResponse.data);
      setJobsData(jobsResponse.data);
    } catch (error) {
      console.error('Failed to fetch pipeline status:', error);
    }
  };

  const fetchServiceStatus = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/test-services`);
      setServiceStatus(response.data);
    } catch (error) {
      console.error('Failed to fetch service status:', error);
    }
  };

  const fetchLogs = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/logs?lines=100`);
      setLogs(response.data.logs);
    } catch (error) {
      console.error('Failed to fetch logs:', error);
    }
  };

  const fetchMissingTaxonomy = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/missing-taxonomy`);
      setMissingTaxonomy(response.data.missing_items);
    } catch (error) {
      console.error('Failed to fetch missing taxonomy:', error);
    }
  };

  const fetchScrapingResults = async (spiderName) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/scraping-results/${spiderName}`);
      setScrapingResults(prev => ({
        ...prev,
        [spiderName]: response.data
      }));
    } catch (error) {
      console.error('Failed to fetch scraping results:', error);
    }
  };

  const startScrapingJob = async () => {
    if (!selectedSpider) {
      alert('Please select a spider');
      return;
    }

    setLoading(true);
    try {
      const response = await axios.post(`${API_BASE_URL}/api/start-scraping`, {
        spider_name: selectedSpider,
        max_items: maxItems,
        start_page: startPage,
        max_pages: maxPages > 0 ? maxPages : null
      });

      if (response.data.success) {
        alert(`Scraping job started: ${response.data.job_id}`);
        fetchPipelineStatus();
      } else {
        alert(`Failed to start scraping: ${response.data.message}`);
      }
    } catch (error) {
      alert(`Error starting scraping job: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Phase 3 Enhanced Functions
  const fetchCapabilities = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/capabilities`);
      setCapabilities(response.data);
    } catch (error) {
      console.error('Failed to fetch capabilities:', error);
    }
  };

  const fetchPerformanceDashboard = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/performance-dashboard`);
      setPerformanceDashboard(response.data);
    } catch (error) {
      console.error('Failed to fetch performance dashboard:', error);
    }
  };

  const fetchPaginationState = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/pagination-state`);
      setPaginationState(response.data);
    } catch (error) {
      console.error('Failed to fetch pagination state:', error);
    }
  };

  const useRecommendedStartPage = () => {
    if (selectedSpider && paginationState.spiders && paginationState.spiders[selectedSpider]) {
      const recommendedPage = paginationState.spiders[selectedSpider].next_start_page;
      setStartPage(recommendedPage);
    }
  };

  const startEnhancedScrapingJob = async () => {
    if (toolsToProcess.length === 0) {
      alert('Please add at least one tool to process');
      return;
    }

    setLoading(true);
    try {
      const response = await axios.post(`${API_BASE_URL}/api/start-enhanced-scraping`, {
        tools: toolsToProcess,
        use_parallel: useParallel,
        use_phase3: usePhase3
      });

      if (response.data.success) {
        const jobId = response.data.job_id;
        setEnhancedJobs(prev => ({
          ...prev,
          [jobId]: {
            ...response.data,
            status: 'running',
            progress: 0
          }
        }));
        alert(`Enhanced scraping job started: ${jobId}`);
      } else {
        alert(`Failed to start enhanced scraping: ${response.data.message}`);
      }
    } catch (error) {
      alert(`Error starting enhanced scraping job: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const updateEnhancedJobStatuses = async () => {
    const enhancedJobIds = Object.keys(enhancedJobs);
    const singleUrlJobIds = Object.keys(singleUrlJobs);
    const allJobIds = [...enhancedJobIds, ...singleUrlJobIds];

    console.log('🔄 Polling jobs:', { enhancedJobIds, singleUrlJobIds, allJobIds });

    for (const jobId of allJobIds) {
      const isEnhancedJob = enhancedJobIds.includes(jobId);
      const currentJob = isEnhancedJob ? enhancedJobs[jobId] : singleUrlJobs[jobId];
      console.log(`📊 Checking job ${jobId}:`, { isEnhancedJob, status: currentJob.status });

      // Only poll running jobs
      if (currentJob.status === 'running') {
        try {
          console.log(`📡 Polling job status: ${jobId}`);
          const response = await axios.get(`${API_BASE_URL}/api/job-status/${jobId}`);
          const updatedJob = response.data;
          console.log(`📨 Job status response for ${jobId}:`, updatedJob);

          if (isEnhancedJob) {
            setEnhancedJobs(prev => ({
              ...prev,
              [jobId]: {
                ...prev[jobId],
                ...updatedJob
              }
            }));
          } else {
            setSingleUrlJobs(prev => {
              const updated = {
                ...prev,
                [jobId]: {
                  ...prev[jobId],
                  ...updatedJob
                }
              };
              console.log(`📊 Updated single URL job ${jobId}:`, updated[jobId]);
              return updated;
            });
          }

          // If job completed, show notification
          if (updatedJob.status === 'completed' && currentJob.status === 'running') {
            console.log(`✅ Enhanced job ${jobId} completed successfully`);

            // Show detailed completion notification
            const results = updatedJob.results;
            if (results) {
              const successRate = results.success_rate || 0;
              const dbSaveRate = results.database_save_rate || 0;
              const totalTools = results.total_tools || 0;

              alert(`🎉 Enhanced Processing Complete!\n\n` +
                    `✅ ${totalTools} tools processed\n` +
                    `📊 ${successRate.toFixed(1)}% processing success rate\n` +
                    `💾 ${dbSaveRate.toFixed(1)}% database save rate\n` +
                    `⏱️ Completed in ${(results.total_processing_time || 0).toFixed(1)}s\n\n` +
                    `All enhanced data has been saved to the database!`);
            } else {
              alert(`✅ Enhanced job ${jobId} completed successfully!`);
            }
          } else if (updatedJob.status === 'failed' && currentJob.status === 'running') {
            console.log(`❌ Enhanced job ${jobId} failed`);
            alert(`❌ Enhanced job ${jobId} failed: ${updatedJob.error || 'Unknown error'}`);
          }

        } catch (error) {
          console.error(`Failed to fetch status for job ${jobId}:`, error);
          // Mark job as failed if we can't fetch status
          setEnhancedJobs(prev => ({
            ...prev,
            [jobId]: {
              ...prev[jobId],
              status: 'failed',
              error: 'Failed to fetch job status'
            }
          }));
        }
      }
    }
  };

  // NEW: Helper function to find existing enhanced job for a traditional job
  const findExistingEnhancedJob = (traditionalJobId) => {
    return Object.entries(enhancedJobs).find(([jobId, job]) =>
      job.source_job_id === traditionalJobId ||
      job.workflow_type === 'traditional_to_enhanced'
    );
  };

  // NEW: Helper function to get pending tools from an existing job
  const getPendingToolsFromJob = (job) => {
    if (!job.tools_with_status) return [];
    return job.tools_with_status.filter(tool =>
      tool.status === 'pending' || tool.status === 'failed'
    );
  };

  const processTraditionalResults = async (jobId) => {
    setLoading(true);
    try {
      // NEW: Check if there's already an enhanced job for this traditional job
      const existingJob = findExistingEnhancedJob(jobId);

      if (existingJob) {
        const [existingJobId, existingJobData] = existingJob;
        const pendingTools = getPendingToolsFromJob(existingJobData);

        if (pendingTools.length === 0) {
          alert(`✅ All tools from this job have already been processed!\n\n` +
                `Enhanced Job: ${existingJobId}\n` +
                `Status: ${existingJobData.status}\n` +
                `Completed: ${existingJobData.tool_summary?.completed || 0} tools\n` +
                `Failed: ${existingJobData.tool_summary?.failed || 0} tools\n\n` +
                `No further processing needed.`);
          setLoading(false);
          return;
        } else {
          const confirmed = window.confirm(
            `⚠️ Found existing enhanced job with some incomplete tools!\n\n` +
            `Enhanced Job: ${existingJobId}\n` +
            `Pending/Failed tools: ${pendingTools.length}\n` +
            `Completed tools: ${existingJobData.tool_summary?.completed || 0}\n\n` +
            `Do you want to reprocess only the pending/failed tools?`
          );

          if (!confirmed) {
            setLoading(false);
            return;
          }
        }
      }

      const response = await axios.post(`${API_BASE_URL}/api/process-traditional-results`, {
        source_job_id: jobId,
        use_parallel: useParallel,
        use_phase3: usePhase3
      });

      if (response.data.success) {
        const enhancedJobId = response.data.job_id;
        const toolsToProcess = response.data.tools_to_process || response.data.total_tools;

        setEnhancedJobs(prev => ({
          ...prev,
          [enhancedJobId]: {
            ...response.data,
            status: 'running',
            progress: 0
          }
        }));

        alert(`🚀 Started Enhanced Processing!\n\n` +
              `Job ID: ${enhancedJobId}\n` +
              `Processing: ${toolsToProcess} tools (out of ${response.data.total_tools} total)\n` +
              `Mode: ${response.data.processing_mode}\n` +
              `Phase 3: ${response.data.phase3_enabled ? 'Enabled' : 'Disabled'}\n` +
              `Estimated time: ${response.data.estimated_time}s\n\n` +
              `Check the Enhanced tab to monitor progress!`);
      } else {
        alert(`Failed to start enhanced processing: ${response.data.error}`);
      }
    } catch (error) {
      alert(`Error starting enhanced processing: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const clearCompletedJobs = () => {
    const confirmed = window.confirm(
      "Are you sure you want to clear all completed jobs?\n\n" +
      "This will remove them from the display but won't affect the database."
    );

    if (confirmed) {
      setEnhancedJobs(prev => {
        const filtered = {};
        Object.entries(prev).forEach(([jobId, job]) => {
          // Keep only running jobs
          if (job.status === 'running') {
            filtered[jobId] = job;
          }
        });
        return filtered;
      });
    }
  };

  const addTool = () => {
    setToolsToProcess(prev => [...prev, { name: '', url: '' }]);
  };

  const removeTool = (index) => {
    setToolsToProcess(prev => prev.filter((_, i) => i !== index));
  };

  const updateTool = (index, field, value) => {
    setToolsToProcess(prev => prev.map((tool, i) =>
      i === index ? { ...tool, [field]: value } : tool
    ));
  };

  const processSingleUrl = async () => {
    console.log('🚀 processSingleUrl called');
    if (!singleUrl.trim()) {
      alert('Please enter a URL');
      return;
    }

    // Auto-generate name from URL if not provided
    const toolName = singleUrlName.trim() || new URL(singleUrl).hostname.replace('www.', '');
    console.log('📝 Processing:', { toolName, url: singleUrl.trim(), useParallel, usePhase3 });

    setLoading(true);
    try {
      console.log('📡 Making API request to:', `${API_BASE_URL}/api/process-single-url`);
      const response = await axios.post(`${API_BASE_URL}/api/process-single-url`, {
        name: toolName,
        url: singleUrl.trim(),
        use_parallel: useParallel,
        use_phase3: usePhase3
      });

      console.log('📨 API Response:', response.data);

      if (response.data.success) {
        const jobId = response.data.job_id;
        console.log('✅ Job created successfully:', jobId);

        setSingleUrlJobs(prev => {
          const newJobs = {
            ...prev,
            [jobId]: {
              ...response.data,
              status: 'running',
              progress: 0,
              url: singleUrl.trim(),
              name: toolName
            }
          };
          console.log('📊 Updated singleUrlJobs:', newJobs);
          return newJobs;
        });

        alert(`🚀 Started processing URL!\n\n` +
              `Job ID: ${jobId}\n` +
              `Tool: ${toolName}\n` +
              `URL: ${singleUrl}\n` +
              `Phase 3: ${response.data.phase3_enabled ? 'Enabled' : 'Disabled'}\n\n` +
              `The tool will be checked for existence, enhanced with AI, and saved to the database.`);

        // Clear the form
        setSingleUrl('');
        setSingleUrlName('');
      } else {
        console.error('❌ Job creation failed:', response.data);
        alert(`Failed to start URL processing: ${response.data.error}`);
      }
    } catch (error) {
      console.error('❌ API Error:', error);
      alert(`Error processing URL: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const StatusIndicator = ({ status, label }) => {
    const getStatusColor = (status) => {
      if (status === 'success') return 'bg-green-500';
      if (status === 'error') return 'bg-red-500';
      return 'bg-yellow-500';
    };

    return (
      <div className="flex items-center space-x-2">
        <div className={`w-3 h-3 rounded-full ${getStatusColor(status)}`}></div>
        <span className="text-sm font-medium">{label}</span>
      </div>
    );
  };

  const ServiceStatusCard = ({ title, status }) => (
    <div className="bg-white p-4 rounded-lg shadow-md border-l-4 border-blue-500">
      <h3 className="font-semibold text-gray-800 mb-2">{title}</h3>
      <StatusIndicator status={status.status} label={status.status} />
      {status.error && (
        <p className="text-red-600 text-sm mt-2">{status.error}</p>
      )}
      {status.categories_count && (
        <p className="text-gray-600 text-sm mt-1">Categories: {status.categories_count}</p>
      )}
      {status.tags_loaded && (
        <p className="text-gray-600 text-sm">Tags: {status.tags_loaded}</p>
      )}
      {status.features_loaded && (
        <p className="text-gray-600 text-sm">Features: {status.features_loaded}</p>
      )}
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">AI Navigator Scrapers</h1>
              <p className="text-gray-600">
                Automated data collection for AI tools directory
                {capabilities.enhanced_scraping && (
                  <span className="ml-2 px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                    Phase 3 Enhanced
                  </span>
                )}
              </p>
            </div>
            <div className="flex items-center space-x-4">
              {pipelineStatus.is_running && (
                <div className="flex items-center space-x-2 bg-green-100 px-3 py-2 rounded-lg">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="text-green-800 text-sm font-medium">Running</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Navigation */}
      <nav className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex space-x-8">
            {['dashboard', 'enhanced', 'scraping', 'results', 'logs', 'taxonomy'].map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.charAt(0).toUpperCase() + tab.slice(1)}
              </button>
            ))}
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        {/* Dashboard Tab */}
        {activeTab === 'dashboard' && (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {Object.entries(serviceStatus).map(([service, status]) => (
                <ServiceStatusCard 
                  key={service}
                  title={service.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                  status={status}
                />
              ))}
            </div>

            {/* Current Job Status */}
            {pipelineStatus.current_job && (
              <div className="bg-white p-6 rounded-lg shadow-md">
                <h3 className="text-lg font-semibold mb-4">Current Job Status</h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">
                      {pipelineStatus.stats?.total_processed || 0}
                    </div>
                    <div className="text-sm text-gray-600">Processed</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">
                      {pipelineStatus.stats?.successful_submissions || 0}
                    </div>
                    <div className="text-sm text-gray-600">Successful</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-red-600">
                      {pipelineStatus.stats?.failed_submissions || 0}
                    </div>
                    <div className="text-sm text-gray-600">Failed</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-yellow-600">
                      {pipelineStatus.stats?.duplicates_skipped || 0}
                    </div>
                    <div className="text-sm text-gray-600">Skipped</div>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Enhanced Phase 3 Tab */}
        {activeTab === 'enhanced' && (
          <div className="space-y-6">
            {/* Phase 3 Capabilities */}
            <div className="bg-white p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold mb-4">🚀 Phase 3 Enhanced Features</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {capabilities.phase3_features && Object.entries(capabilities.phase3_features).map(([feature, available]) => (
                  <div key={feature} className="flex items-center space-x-2">
                    <div className={`w-3 h-3 rounded-full ${available ? 'bg-green-500' : 'bg-red-500'}`}></div>
                    <span className="text-sm">{feature.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Enhanced Scraping Controls */}
            <div className="bg-white p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold mb-4">Enhanced Tool Processing</h3>

              {/* Tools Configuration */}
              <div className="mb-6">
                <div className="flex justify-between items-center mb-4">
                  <h4 className="font-medium">Tools to Process</h4>
                  <button
                    onClick={addTool}
                    className="bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600"
                  >
                    Add Tool
                  </button>
                </div>

                <div className="space-y-3">
                  {toolsToProcess.map((tool, index) => (
                    <div key={index} className="flex space-x-3 items-center">
                      <input
                        type="text"
                        placeholder="Tool Name"
                        value={tool.name}
                        onChange={(e) => updateTool(index, 'name', e.target.value)}
                        className="flex-1 border border-gray-300 rounded px-3 py-2"
                      />
                      <input
                        type="url"
                        placeholder="Tool URL"
                        value={tool.url}
                        onChange={(e) => updateTool(index, 'url', e.target.value)}
                        className="flex-1 border border-gray-300 rounded px-3 py-2"
                      />
                      <button
                        onClick={() => removeTool(index)}
                        className="bg-red-500 text-white px-3 py-2 rounded hover:bg-red-600"
                      >
                        Remove
                      </button>
                    </div>
                  ))}
                </div>
              </div>

              {/* Processing Options */}
              <div className="mb-6">
                <h4 className="font-medium mb-3">Processing Options</h4>
                <div className="space-y-3">
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={useParallel}
                      onChange={(e) => setUseParallel(e.target.checked)}
                      className="rounded"
                    />
                    <span>Use Parallel Processing (1.6x faster)</span>
                  </label>
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={usePhase3}
                      onChange={(e) => setUsePhase3(e.target.checked)}
                      className="rounded"
                    />
                    <span>Enable Phase 3 Advanced Analysis</span>
                  </label>
                </div>
              </div>

              {/* Start Enhanced Processing */}
              <button
                onClick={startEnhancedScrapingJob}
                disabled={loading || !capabilities.enhanced_scraping}
                className="w-full bg-green-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-green-700 disabled:bg-gray-400"
              >
                {loading ? 'Starting Enhanced Processing...' : 'Start Enhanced Processing'}
              </button>
            </div>

            {/* Single URL Processing */}
            <div className="bg-white p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold mb-4">🎯 Single URL Processing</h3>
              <p className="text-sm text-gray-600 mb-4">
                Process a single tool URL through the complete pipeline: existence check → AI enhancement → database save
              </p>

              {/* URL Input */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tool URL *
                </label>
                <input
                  type="url"
                  value={singleUrl}
                  onChange={(e) => setSingleUrl(e.target.value)}
                  placeholder="https://example.com"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              {/* Tool Name Input */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tool Name (optional)
                </label>
                <input
                  type="text"
                  value={singleUrlName}
                  onChange={(e) => setSingleUrlName(e.target.value)}
                  placeholder="Auto-generated from URL if empty"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              {/* Processing Options */}
              <div className="mb-4 space-y-2">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={useParallel}
                    onChange={(e) => setUseParallel(e.target.checked)}
                    className="mr-2"
                  />
                  <span>Use Parallel Processing</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={usePhase3}
                    onChange={(e) => setUsePhase3(e.target.checked)}
                    className="mr-2"
                  />
                  <span>Enable Phase 3 Advanced Analysis</span>
                </label>
              </div>

              {/* Process Button */}
              <button
                onClick={processSingleUrl}
                disabled={loading || !singleUrl.trim()}
                className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 disabled:bg-gray-400"
              >
                {loading ? 'Processing URL...' : 'Process Single URL'}
              </button>

              <div className="mt-3 p-3 bg-blue-50 rounded-md">
                <p className="text-sm text-blue-800">
                  🔄 <strong>Complete Pipeline:</strong> URL Input → Existence Check → AI Enhancement → Database Save
                </p>
              </div>
            </div>

            {/* Enhanced Job Status */}
            {Object.keys(enhancedJobs).length > 0 && (
              <div className="bg-white p-6 rounded-lg shadow-md">
                <h3 className="text-lg font-semibold mb-4">Enhanced Job Status</h3>
                <div className="space-y-4">
                  {Object.entries(enhancedJobs).map(([jobId, job]) => (
                    <div key={jobId} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex justify-between items-center mb-2">
                        <span className="font-medium">Job: {jobId}</span>
                        <span className={`px-2 py-1 rounded text-sm ${
                          job.status === 'completed' ? 'bg-green-100 text-green-800' :
                          job.status === 'failed' ? 'bg-red-100 text-red-800' :
                          'bg-yellow-100 text-yellow-800'
                        }`}>
                          {job.status}
                        </span>
                      </div>

                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div>
                          <span className="text-gray-600">Tools:</span> {job.total_tools}
                        </div>
                        <div>
                          <span className="text-gray-600">Mode:</span> {job.processing_mode}
                        </div>
                        <div>
                          <span className="text-gray-600">Phase 3:</span> {job.phase3_enabled ? 'Yes' : 'No'}
                        </div>
                        <div>
                          <span className="text-gray-600">Progress:</span> {job.progress?.toFixed(1)}%
                        </div>
                      </div>

                      {job.status === 'running' && (
                        <div className="mt-3">
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                              style={{ width: `${job.progress || 0}%` }}
                            ></div>
                          </div>
                        </div>
                      )}

                      {job.status === 'completed' && job.results && (
                        <div className="mt-3 p-3 bg-gray-50 rounded">
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-3">
                            <div>
                              <span className="text-gray-600">Success Rate:</span> {job.results.success_rate?.toFixed(1)}%
                            </div>
                            <div>
                              <span className="text-gray-600">Avg Time:</span> {job.results.average_processing_time?.toFixed(2)}s
                            </div>
                            <div>
                              <span className="text-gray-600">Cache Hits:</span> {job.results.cache_stats?.hits || 0}
                            </div>
                            <div>
                              <span className="text-gray-600">Phase 3 Elements:</span> {
                                (job.results.phase3_metrics?.structured_data_elements || 0) +
                                (job.results.phase3_metrics?.content_analysis_elements || 0) +
                                (job.results.phase3_metrics?.performance_metrics || 0)
                              }
                            </div>
                          </div>

                          {/* Database Integration Results */}
                          {job.results.database_metrics && (
                            <div className="border-t border-gray-200 pt-3">
                              <h5 className="font-medium text-gray-700 mb-2">Database Integration</h5>
                              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                                <div>
                                  <span className="text-gray-600">DB Save Rate:</span> {job.results.database_save_rate?.toFixed(1)}%
                                </div>
                                <div>
                                  <span className="text-gray-600">Entities Saved:</span> {job.results.database_metrics?.successful_saves || 0}
                                </div>
                                <div>
                                  <span className="text-gray-600">Failed Saves:</span> {job.results.database_metrics?.failed_saves || 0}
                                </div>
                                <div>
                                  <span className="text-gray-600">Avg Confidence:</span> {job.results.database_metrics?.average_confidence?.toFixed(2) || 'N/A'}
                                </div>
                              </div>
                            </div>
                          )}

                          {/* NEW: Individual Tool Status Display */}
                          {job.tools_with_status && job.tools_with_status.length > 0 && (
                            <div className="border-t border-gray-200 pt-3 mt-3">
                              <h5 className="font-medium text-gray-700 mb-2">Individual Tool Status</h5>
                              <div className="space-y-2 max-h-40 overflow-y-auto">
                                {job.tools_with_status.map((tool, index) => (
                                  <div key={index} className="flex items-center justify-between p-2 bg-white rounded border">
                                    <div className="flex-1">
                                      <span className="font-medium text-sm">{tool.name}</span>
                                      <span className="text-xs text-gray-500 ml-2">{tool.url}</span>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                      <span className={`px-2 py-1 rounded text-xs ${
                                        tool.status === 'completed' ? 'bg-green-100 text-green-800' :
                                        tool.status === 'processing' ? 'bg-blue-100 text-blue-800' :
                                        tool.status === 'failed' ? 'bg-red-100 text-red-800' :
                                        'bg-gray-100 text-gray-800'
                                      }`}>
                                        {tool.status === 'completed' ? '✅ Complete' :
                                         tool.status === 'processing' ? '🔄 Processing' :
                                         tool.status === 'failed' ? '❌ Failed' :
                                         '⏳ Pending'}
                                      </span>
                                    </div>
                                  </div>
                                ))}
                              </div>

                              {/* Tool Summary */}
                              {job.tool_summary && (
                                <div className="mt-3 p-2 bg-blue-50 rounded">
                                  <div className="grid grid-cols-4 gap-2 text-xs text-center">
                                    <div>
                                      <div className="font-medium text-green-600">{job.tool_summary.completed}</div>
                                      <div className="text-gray-600">Completed</div>
                                    </div>
                                    <div>
                                      <div className="font-medium text-blue-600">{job.tool_summary.processing}</div>
                                      <div className="text-gray-600">Processing</div>
                                    </div>
                                    <div>
                                      <div className="font-medium text-red-600">{job.tool_summary.failed}</div>
                                      <div className="text-gray-600">Failed</div>
                                    </div>
                                    <div>
                                      <div className="font-medium text-gray-600">{job.tool_summary.pending}</div>
                                      <div className="text-gray-600">Pending</div>
                                    </div>
                                  </div>
                                </div>
                              )}
                            </div>
                          )}

                          {/* Workflow Information */}
                          {job.results.workflow_info && (
                            <div className="border-t border-gray-200 pt-3 mt-3">
                              <h5 className="font-medium text-gray-700 mb-2">Workflow Information</h5>
                              <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                                <div>
                                  <span className="text-gray-600">Type:</span> {job.results.workflow_info.workflow_type?.replace('_', ' → ') || 'Direct'}
                                </div>
                                {job.results.workflow_info.source_job_id && (
                                  <div>
                                    <span className="text-gray-600">Source Job:</span> {job.results.workflow_info.source_job_id}
                                  </div>
                                )}
                                <div>
                                  <span className="text-gray-600">Phase 3:</span> {job.results.workflow_info.phase3_enabled ? '✅ Enabled' : '❌ Disabled'}
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  ))}
                </div>

                {/* Clear Completed Jobs Button */}
                {Object.values(enhancedJobs).some(job => job.status === 'completed' || job.status === 'failed') && (
                  <div className="mt-4 pt-4 border-t border-gray-200">
                    <button
                      onClick={clearCompletedJobs}
                      className="w-full bg-gray-500 text-white py-2 px-4 rounded-lg hover:bg-gray-600"
                    >
                      Clear Completed Jobs
                    </button>
                  </div>
                )}
              </div>
            )}

            {/* Single URL Job Status */}
            {Object.keys(singleUrlJobs).length > 0 && (
              <div className="bg-white p-6 rounded-lg shadow-md">
                <h3 className="text-lg font-semibold mb-4">🎯 Single URL Processing Jobs</h3>
                <div className="space-y-4">
                  {Object.entries(singleUrlJobs).map(([jobId, job]) => (
                    <div key={jobId} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex justify-between items-start mb-3">
                        <div>
                          <h4 className="font-medium text-gray-900">{job.name}</h4>
                          <p className="text-sm text-gray-600 break-all">{job.url}</p>
                          <p className="text-xs text-gray-500">Job ID: {jobId}</p>
                        </div>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          job.status === 'completed' ? 'bg-green-100 text-green-800' :
                          job.status === 'running' ? 'bg-blue-100 text-blue-800' :
                          job.status === 'failed' ? 'bg-red-100 text-red-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {job.status === 'completed' ? '✅ Complete' :
                           job.status === 'running' ? '🔄 Processing' :
                           job.status === 'failed' ? '❌ Failed' :
                           '⏳ Pending'}
                        </span>
                      </div>

                      {/* Progress Bar */}
                      {job.status === 'running' && (
                        <div className="mb-3">
                          <div className="flex justify-between text-sm text-gray-600 mb-1">
                            <span>Progress</span>
                            <span>{job.progress || 0}%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                              style={{ width: `${job.progress || 0}%` }}
                            ></div>
                          </div>
                        </div>
                      )}

                      {/* Results */}
                      {job.results && (
                        <div className="mt-3 p-3 bg-gray-50 rounded">
                          <h5 className="font-medium text-gray-700 mb-2">Results</h5>
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                            <div>
                              <span className="text-gray-600">Status:</span> {job.results.status || 'Processing'}
                            </div>
                            <div>
                              <span className="text-gray-600">Exists:</span> {job.results.exists ? '✅ Yes' : '❌ No'}
                            </div>
                            <div>
                              <span className="text-gray-600">Enhanced:</span> {job.results.enhanced ? '✅ Yes' : '❌ No'}
                            </div>
                            <div>
                              <span className="text-gray-600">Saved:</span> {job.results.saved_to_db ? '✅ Yes' : '❌ No'}
                            </div>
                          </div>

                          {job.results.error && (
                            <div className="mt-2 p-2 bg-red-50 rounded text-sm text-red-700">
                              <strong>Error:</strong> {job.results.error}
                            </div>
                          )}

                          {job.results.message && (
                            <div className="mt-2 p-2 bg-blue-50 rounded text-sm text-blue-700">
                              <strong>Message:</strong> {job.results.message}
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  ))}
                </div>

                {/* Clear Completed Single URL Jobs Button */}
                {Object.values(singleUrlJobs).some(job => job.status === 'completed' || job.status === 'failed') && (
                  <div className="mt-4 pt-4 border-t border-gray-200">
                    <button
                      onClick={() => setSingleUrlJobs(prev =>
                        Object.fromEntries(Object.entries(prev).filter(([_, job]) =>
                          job.status !== 'completed' && job.status !== 'failed'
                        ))
                      )}
                      className="w-full bg-gray-500 text-white py-2 px-4 rounded-lg hover:bg-gray-600"
                    >
                      Clear Completed Single URL Jobs
                    </button>
                  </div>
                )}
              </div>
            )}

            {/* Performance Dashboard */}
            {performanceDashboard.system_metrics && (
              <div className="bg-white p-6 rounded-lg shadow-md">
                <h3 className="text-lg font-semibold mb-4">Performance Dashboard</h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {performanceDashboard.system_metrics.cpu_usage_percent && (
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">
                        {performanceDashboard.system_metrics.cpu_usage_percent.toFixed(1)}%
                      </div>
                      <div className="text-sm text-gray-600">CPU Usage</div>
                    </div>
                  )}
                  {performanceDashboard.system_metrics.memory_usage_percent && (
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">
                        {performanceDashboard.system_metrics.memory_usage_percent.toFixed(1)}%
                      </div>
                      <div className="text-sm text-gray-600">Memory Usage</div>
                    </div>
                  )}
                  {performanceDashboard.alerts && (
                    <div className="text-center">
                      <div className="text-2xl font-bold text-yellow-600">
                        {performanceDashboard.alerts.active_alerts || 0}
                      </div>
                      <div className="text-sm text-gray-600">Active Alerts</div>
                    </div>
                  )}
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600">
                      {performanceDashboard.recommendations?.length || 0}
                    </div>
                    <div className="text-sm text-gray-600">Recommendations</div>
                  </div>
                </div>

                {performanceDashboard.recommendations && performanceDashboard.recommendations.length > 0 && (
                  <div className="mt-4">
                    <h4 className="font-medium mb-2">Optimization Recommendations:</h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      {performanceDashboard.recommendations.slice(0, 3).map((rec, index) => (
                        <li key={index}>• {rec.recommendation}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            )}
          </div>
        )}

        {/* Scraping Tab */}
        {activeTab === 'scraping' && (
          <div className="space-y-6">
            <div className="bg-white p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold mb-4">Start Scraping Job</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Select Spider
                  </label>
                  <select
                    value={selectedSpider}
                    onChange={(e) => setSelectedSpider(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    {spiders.map((spider) => (
                      <option key={spider} value={spider}>
                        {spider}
                      </option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Max Items
                  </label>
                  <input
                    type="number"
                    value={maxItems}
                    onChange={(e) => setMaxItems(parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    min="1"
                    max="1000"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Start Page
                  </label>
                  <input
                    type="number"
                    value={startPage}
                    onChange={(e) => setStartPage(parseInt(e.target.value) || 1)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    min="1"
                    placeholder="1"
                  />
                  <div className="text-xs text-gray-500 mt-1 flex justify-between items-center">
                    <span>
                      {selectedSpider && paginationState.spiders && paginationState.spiders[selectedSpider]
                        ? `Last: page ${paginationState.spiders[selectedSpider].last_page}, next: ${paginationState.spiders[selectedSpider].next_start_page}`
                        : 'No previous state'}
                    </span>
                    {selectedSpider && paginationState.spiders && paginationState.spiders[selectedSpider] && (
                      <button
                        onClick={useRecommendedStartPage}
                        className="text-blue-600 hover:text-blue-800 text-xs underline"
                      >
                        Use Next
                      </button>
                    )}
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Max Pages
                  </label>
                  <input
                    type="number"
                    value={maxPages}
                    onChange={(e) => setMaxPages(parseInt(e.target.value) || 0)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    min="0"
                    placeholder="0 = unlimited"
                  />
                  <div className="text-xs text-gray-500 mt-1">
                    0 = unlimited pages
                  </div>
                </div>
                <div className="flex items-end">
                  <button
                    onClick={startScrapingJob}
                    disabled={loading || pipelineStatus.is_running}
                    className={`w-full px-4 py-2 rounded-md font-medium ${
                      loading || pipelineStatus.is_running
                        ? 'bg-gray-400 cursor-not-allowed'
                        : 'bg-blue-600 hover:bg-blue-700'
                    } text-white`}
                  >
                    {loading ? 'Starting...' : 'Start Scraping'}
                  </button>
                </div>
              </div>
              
              {pipelineStatus.is_running && (
                <div className="bg-blue-50 p-4 rounded-md">
                  <p className="text-blue-800">
                    Job is currently running: {pipelineStatus.current_job?.spider_name || 'Unknown'}
                    {pipelineStatus.current_job?.status && ` (${pipelineStatus.current_job.status})`}
                  </p>
                </div>
              )}
            </div>

            {/* Traditional to Enhanced Workflow */}
            <div className="bg-white p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold mb-4">Traditional → Enhanced Workflow</h3>
              <p className="text-gray-600 mb-4">
                Process completed traditional scraping results through the enhanced AI pipeline and save to database.
              </p>

              {/* Real traditional jobs from API */}
              <div className="space-y-3">
                {console.log('🔍 DEBUG: jobsData.traditional_jobs:', jobsData.traditional_jobs)}
                {jobsData.traditional_jobs && Object.entries(jobsData.traditional_jobs).length > 0 ? (
                  Object.entries(jobsData.traditional_jobs).map(([jobId, jobData]) => {
                    const traditionalJob = {
                      id: jobId,
                      name: jobId,
                      tools: jobData.results?.results_count || 0,
                      time: jobData.start_time ? new Date(jobData.start_time * 1000).toLocaleString() : 'Unknown',
                      status: jobData.status
                    };
                  const existingEnhancedJob = findExistingEnhancedJob(traditionalJob.id);
                  const enhancementStatus = existingEnhancedJob ? existingEnhancedJob[1] : null;

                  return (
                    <div key={traditionalJob.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex justify-between items-center">
                        <div className="flex-1">
                          <h4 className="font-medium">{traditionalJob.name}</h4>
                          <p className="text-sm text-gray-500">
                            Completed • {traditionalJob.tools} tools scraped • {traditionalJob.time}
                          </p>

                          {/* NEW: Enhancement Status Display */}
                          {enhancementStatus && (
                            <div className="mt-2 p-2 bg-gray-50 rounded text-sm">
                              <div className="flex items-center justify-between">
                                <span className="text-gray-600">Enhancement Status:</span>
                                <span className={`px-2 py-1 rounded text-xs ${
                                  enhancementStatus.status === 'completed' ? 'bg-green-100 text-green-800' :
                                  enhancementStatus.status === 'failed' ? 'bg-red-100 text-red-800' :
                                  'bg-yellow-100 text-yellow-800'
                                }`}>
                                  {enhancementStatus.status}
                                </span>
                              </div>
                              {enhancementStatus.tool_summary && (
                                <div className="mt-1 grid grid-cols-4 gap-2 text-xs">
                                  <div className="text-center">
                                    <div className="font-medium text-green-600">{enhancementStatus.tool_summary.completed}</div>
                                    <div className="text-gray-500">Done</div>
                                  </div>
                                  <div className="text-center">
                                    <div className="font-medium text-blue-600">{enhancementStatus.tool_summary.processing}</div>
                                    <div className="text-gray-500">Processing</div>
                                  </div>
                                  <div className="text-center">
                                    <div className="font-medium text-red-600">{enhancementStatus.tool_summary.failed}</div>
                                    <div className="text-gray-500">Failed</div>
                                  </div>
                                  <div className="text-center">
                                    <div className="font-medium text-gray-600">{enhancementStatus.tool_summary.pending}</div>
                                    <div className="text-gray-500">Pending</div>
                                  </div>
                                </div>
                              )}
                            </div>
                          )}
                        </div>

                        <button
                          onClick={() => processTraditionalResults(traditionalJob.id)}
                          disabled={loading}
                          className={`px-4 py-2 rounded-md font-medium ${
                            enhancementStatus && enhancementStatus.tool_summary?.pending === 0 && enhancementStatus.tool_summary?.failed === 0
                              ? 'bg-gray-400 text-gray-600 cursor-not-allowed'
                              : 'bg-green-600 text-white hover:bg-green-700 disabled:bg-gray-400'
                          }`}
                        >
                          {loading ? 'Processing...' :
                           enhancementStatus && enhancementStatus.tool_summary?.pending === 0 && enhancementStatus.tool_summary?.failed === 0
                             ? '✅ All Enhanced'
                             : enhancementStatus && (enhancementStatus.tool_summary?.pending > 0 || enhancementStatus.tool_summary?.failed > 0)
                               ? `Enhance Remaining (${(enhancementStatus.tool_summary?.pending || 0) + (enhancementStatus.tool_summary?.failed || 0)})`
                               : 'Enhance & Save to DB'}
                        </button>
                      </div>
                    </div>
                  );
                  })
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <p>No completed traditional scraping jobs found.</p>
                    <p className="text-sm mt-2">Start a traditional scraping job above to see results here.</p>
                  </div>
                )}
              </div>

              <div className="mt-4 p-3 bg-blue-50 rounded-md">
                <p className="text-sm text-blue-800">
                  💡 <strong>Complete Workflow:</strong> Traditional Scraping → Enhanced AI Processing → Database Storage
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Results Tab */}
        {activeTab === 'results' && (
          <div className="space-y-6">
            <div className="bg-white p-6 rounded-lg shadow-md">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold">Scraping Results</h3>
                <div className="flex space-x-2">
                  {spiders.map((spider) => (
                    <button
                      key={spider}
                      onClick={() => fetchScrapingResults(spider)}
                      className="px-3 py-1 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200"
                    >
                      Load {spider}
                    </button>
                  ))}
                </div>
              </div>
              
              {Object.entries(scrapingResults).map(([spiderName, results]) => (
                <div key={spiderName} className="mb-6">
                  <h4 className="font-medium text-gray-800 mb-2">
                    {spiderName} ({results?.results_count || 0} results)
                  </h4>
                  <div className="max-h-64 overflow-y-auto bg-gray-50 p-4 rounded-md">
                    {results?.results && Array.isArray(results.results) ? (
                      results.results.map((result, index) => (
                        <div key={index} className="mb-2 p-2 bg-white rounded border">
                          <div className="font-medium">{result.tool_name_on_directory || 'Unknown Tool'}</div>
                          <div className="text-sm text-gray-600 truncate">
                            {result.external_website_url || 'No URL'}
                          </div>
                          <div className="text-xs text-gray-500">
                            {result.scraped_date ? new Date(result.scraped_date).toLocaleString() : 'No date'}
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="text-gray-500 text-center py-4">
                        {typeof results === 'object' ? (
                          <pre className="text-xs text-left bg-gray-100 p-2 rounded overflow-auto max-h-32">
                            {JSON.stringify(results, null, 2)}
                          </pre>
                        ) : (
                          'No results available or invalid data format'
                        )}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Logs Tab */}
        {activeTab === 'logs' && (
          <div className="space-y-6">
            <div className="bg-white p-6 rounded-lg shadow-md">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold">System Logs</h3>
                <button
                  onClick={fetchLogs}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  Refresh Logs
                </button>
              </div>
              <div className="bg-gray-900 text-green-400 p-4 rounded-md max-h-96 overflow-y-auto font-mono text-sm">
                {logs.map((log, index) => (
                  <div key={index} className="mb-1">
                    {log}
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Taxonomy Tab */}
        {activeTab === 'taxonomy' && (
          <div className="space-y-6">
            <div className="bg-white p-6 rounded-lg shadow-md">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold">Missing Taxonomy Items</h3>
                <button
                  onClick={fetchMissingTaxonomy}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  Refresh
                </button>
              </div>
              
              {missingTaxonomy.length > 0 ? (
                <div className="max-h-64 overflow-y-auto">
                  {missingTaxonomy.map((item, index) => (
                    <div key={index} className="mb-2 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                      <div className="flex justify-between items-center">
                        <div>
                          <span className="font-medium text-yellow-800">{item.type}</span>
                          <span className="ml-2 text-gray-700">{item.name}</span>
                        </div>
                        <span className="text-sm text-gray-500">{item.timestamp}</span>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-600">No missing taxonomy items found.</p>
              )}
            </div>
          </div>
        )}
      </main>
    </div>
  );
}

export default App;