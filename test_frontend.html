<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Frontend API Connection</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .test-section { background: #f5f5f5; padding: 20px; margin: 20px 0; border-radius: 8px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        button { padding: 10px 20px; margin: 10px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        #results { background: white; padding: 20px; border: 1px solid #ddd; border-radius: 4px; margin-top: 20px; }
    </style>
</head>
<body>
    <h1>🧪 Frontend API Connection Test</h1>
    
    <div class="test-section">
        <h3>Test 1: Health Check</h3>
        <button onclick="testHealth()">Test Health Endpoint</button>
        <div id="health-result"></div>
    </div>

    <div class="test-section">
        <h3>Test 2: File Upload Endpoint</h3>
        <input type="file" id="testFile" accept=".csv,.xlsx,.xls">
        <button onclick="testFileUpload()">Test File Upload</button>
        <div id="upload-result"></div>
    </div>

    <div class="test-section">
        <h3>Test 3: Single URL Processing</h3>
        <button onclick="testSingleUrl()">Test Single URL</button>
        <div id="single-url-result"></div>
    </div>

    <div id="results">
        <h3>📋 Test Results:</h3>
        <div id="log"></div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8001';
        
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            logDiv.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        async function testHealth() {
            const resultDiv = document.getElementById('health-result');
            try {
                log('🔍 Testing health endpoint...', 'info');
                const response = await fetch(`${API_BASE_URL}/api/health`);
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `<div class="success">✅ Health check passed: ${JSON.stringify(data)}</div>`;
                    log('✅ Health check successful', 'success');
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ Health check failed: ${response.status}</div>`;
                    log(`❌ Health check failed: ${response.status}`, 'error');
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Network error: ${error.message}</div>`;
                log(`❌ Health check error: ${error.message}`, 'error');
            }
        }

        async function testFileUpload() {
            const resultDiv = document.getElementById('upload-result');
            const fileInput = document.getElementById('testFile');
            
            if (!fileInput.files[0]) {
                // Create a test CSV file
                const csvContent = 'name,url\\nTest Tool,https://example.com';
                const blob = new Blob([csvContent], { type: 'text/csv' });
                const file = new File([blob], 'test.csv', { type: 'text/csv' });
                
                const formData = new FormData();
                formData.append('file', file);
                formData.append('use_parallel', 'true');
                formData.append('use_phase3', 'true');
                
                try {
                    log('🔍 Testing file upload endpoint with generated CSV...', 'info');
                    const response = await fetch(`${API_BASE_URL}/api/process-file-upload`, {
                        method: 'POST',
                        body: formData
                    });
                    
                    const data = await response.json();
                    
                    if (response.ok) {
                        resultDiv.innerHTML = `<div class="success">✅ File upload successful: ${JSON.stringify(data)}</div>`;
                        log('✅ File upload successful', 'success');
                    } else {
                        resultDiv.innerHTML = `<div class="error">❌ File upload failed: ${response.status} - ${JSON.stringify(data)}</div>`;
                        log(`❌ File upload failed: ${response.status}`, 'error');
                    }
                } catch (error) {
                    resultDiv.innerHTML = `<div class="error">❌ Network error: ${error.message}</div>`;
                    log(`❌ File upload error: ${error.message}`, 'error');
                }
            } else {
                // Use selected file
                const formData = new FormData();
                formData.append('file', fileInput.files[0]);
                formData.append('use_parallel', 'true');
                formData.append('use_phase3', 'true');
                
                try {
                    log(`🔍 Testing file upload endpoint with ${fileInput.files[0].name}...`, 'info');
                    const response = await fetch(`${API_BASE_URL}/api/process-file-upload`, {
                        method: 'POST',
                        body: formData
                    });
                    
                    const data = await response.json();
                    
                    if (response.ok) {
                        resultDiv.innerHTML = `<div class="success">✅ File upload successful: ${JSON.stringify(data)}</div>`;
                        log('✅ File upload successful', 'success');
                    } else {
                        resultDiv.innerHTML = `<div class="error">❌ File upload failed: ${response.status} - ${JSON.stringify(data)}</div>`;
                        log(`❌ File upload failed: ${response.status}`, 'error');
                    }
                } catch (error) {
                    resultDiv.innerHTML = `<div class="error">❌ Network error: ${error.message}</div>`;
                    log(`❌ File upload error: ${error.message}`, 'error');
                }
            }
        }

        async function testSingleUrl() {
            const resultDiv = document.getElementById('single-url-result');
            try {
                log('🔍 Testing single URL endpoint...', 'info');
                const response = await fetch(`${API_BASE_URL}/api/process-single-url`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        name: 'Test Tool',
                        url: 'https://example.com',
                        use_parallel: true,
                        use_phase3: true
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `<div class="success">✅ Single URL processing successful: ${JSON.stringify(data)}</div>`;
                    log('✅ Single URL processing successful', 'success');
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ Single URL processing failed: ${response.status} - ${JSON.stringify(data)}</div>`;
                    log(`❌ Single URL processing failed: ${response.status}`, 'error');
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Network error: ${error.message}</div>`;
                log(`❌ Single URL processing error: ${error.message}`, 'error');
            }
        }

        // Initialize
        log('🌐 Frontend API Test Interface Ready', 'info');
        log(`🔗 Testing connection to: ${API_BASE_URL}`, 'info');
    </script>
</body>
</html>
