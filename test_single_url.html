<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Single URL Processing</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, button { padding: 10px; font-size: 16px; }
        input[type="text"], input[type="url"] { width: 100%; box-sizing: border-box; }
        button { background: #007cba; color: white; border: none; cursor: pointer; }
        button:hover { background: #005a87; }
        button:disabled { background: #ccc; cursor: not-allowed; }
        .job-status { margin-top: 20px; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .progress-bar { width: 100%; height: 20px; background: #f0f0f0; border-radius: 10px; overflow: hidden; }
        .progress-fill { height: 100%; background: #007cba; transition: width 0.3s ease; }
        .log { background: #f5f5f5; padding: 10px; margin-top: 10px; border-radius: 5px; font-family: monospace; font-size: 12px; max-height: 200px; overflow-y: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 Test Single URL Processing</h1>
        
        <div class="form-group">
            <label for="toolName">Tool Name (optional):</label>
            <input type="text" id="toolName" placeholder="Auto-generated from URL if empty">
        </div>
        
        <div class="form-group">
            <label for="toolUrl">Tool URL:</label>
            <input type="url" id="toolUrl" placeholder="https://example.com" required>
        </div>
        
        <div class="form-group">
            <label>
                <input type="checkbox" id="useParallel" checked> Use Parallel Processing
            </label>
        </div>
        
        <div class="form-group">
            <label>
                <input type="checkbox" id="usePhase3" checked> Enable Phase 3 Advanced Analysis
            </label>
        </div>
        
        <button onclick="processUrl()" id="processBtn">Process Single URL</button>
        
        <div id="jobStatus" class="job-status" style="display: none;">
            <h3>Job Status</h3>
            <div><strong>Job ID:</strong> <span id="jobId"></span></div>
            <div><strong>Status:</strong> <span id="status"></span></div>
            <div><strong>Progress:</strong> <span id="progressText">0%</span></div>
            <div class="progress-bar">
                <div id="progressFill" class="progress-fill" style="width: 0%"></div>
            </div>
            <div><strong>Current Step:</strong> <span id="currentStep">-</span></div>
        </div>
        
        <div id="log" class="log"></div>
    </div>

    <script>
        let currentJobId = null;
        let pollInterval = null;

        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        async function processUrl() {
            const toolName = document.getElementById('toolName').value.trim();
            const toolUrl = document.getElementById('toolUrl').value.trim();
            const useParallel = document.getElementById('useParallel').checked;
            const usePhase3 = document.getElementById('usePhase3').checked;

            if (!toolUrl) {
                alert('Please enter a URL');
                return;
            }

            const finalName = toolName || new URL(toolUrl).hostname.replace('www.', '');
            
            log(`🚀 Starting processing for: ${finalName} - ${toolUrl}`);
            
            document.getElementById('processBtn').disabled = true;
            
            try {
                const response = await fetch('/api/process-single-url', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        name: finalName,
                        url: toolUrl,
                        use_parallel: useParallel,
                        use_phase3: usePhase3
                    })
                });

                const data = await response.json();
                log(`📨 API Response: ${JSON.stringify(data)}`);

                if (data.success) {
                    currentJobId = data.job_id;
                    document.getElementById('jobId').textContent = currentJobId;
                    document.getElementById('jobStatus').style.display = 'block';
                    
                    log(`✅ Job created successfully: ${currentJobId}`);
                    
                    // Start polling
                    startPolling();
                } else {
                    log(`❌ Failed to create job: ${data.error}`);
                    alert(`Failed to start processing: ${data.error}`);
                }
            } catch (error) {
                log(`❌ Error: ${error.message}`);
                alert(`Error: ${error.message}`);
            } finally {
                document.getElementById('processBtn').disabled = false;
            }
        }

        function startPolling() {
            if (pollInterval) clearInterval(pollInterval);
            
            pollInterval = setInterval(async () => {
                if (!currentJobId) return;
                
                try {
                    log(`🔄 Polling job status: ${currentJobId}`);
                    const response = await fetch(`/api/job-status/${currentJobId}`);
                    const data = await response.json();
                    
                    log(`📊 Status update: ${JSON.stringify(data)}`);
                    
                    updateJobDisplay(data);
                    
                    if (data.status === 'completed' || data.status === 'failed') {
                        clearInterval(pollInterval);
                        log(`🏁 Job finished with status: ${data.status}`);
                    }
                } catch (error) {
                    log(`❌ Polling error: ${error.message}`);
                }
            }, 2000); // Poll every 2 seconds
        }

        function updateJobDisplay(data) {
            document.getElementById('status').textContent = data.status || 'unknown';
            
            const progress = data.progress || 0;
            document.getElementById('progressText').textContent = `${progress}%`;
            document.getElementById('progressFill').style.width = `${progress}%`;
            
            const currentStep = data.results?.status || 'Initializing...';
            document.getElementById('currentStep').textContent = currentStep;
        }

        // Test backend connectivity on load
        window.onload = async () => {
            try {
                const response = await fetch('/api/health');
                const data = await response.json();
                log(`✅ Backend connected: ${data.status}`);
            } catch (error) {
                log(`❌ Backend connection failed: ${error.message}`);
            }
        };
    </script>
</body>
</html>
